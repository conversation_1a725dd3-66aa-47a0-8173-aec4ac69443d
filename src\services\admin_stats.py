# src/services/admin_stats.py
from __future__ import annotations

from typing import Dict, Iterable, List, Tuple, Set
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.sql import functions as sfunc
from sqlalchemy import func, case, Integer
from sqlalchemy.orm import aliased

from src.DB.models.assessments import (
    Course, CourseContent, Lesson, Assessment,
    UserSession, Response, Option, UserLessonProgress
)
from src.schemas.stats import (
    CourseEngagement, LessonEngagement,
    AssessmentPerformance, AssessmentRetryStat,
    CourseCompletionStat
)


# ------------------------
# Helpers
# ------------------------

async def _sessions_correct_ratio_map(db: AsyncSession, session_ids: Iterable[UUID]) -> Dict[UUID, float]:
    """Return {session_id: correct_ratio(0..1)} based on Option.is_correct."""
    session_ids = list({sid for sid in session_ids if sid})
    if not session_ids:
        return {}

    sub = (
        select(
            Response.session_id.label("sid"),
            func.count(Response.response_id).label("total"),
            func.sum(
                case((Option.is_correct.is_(True), 1), else_=0).cast(Integer)
            ).label("correct")
        )
        .select_from(Response.__table__.outerjoin(Option, Response.option_id == Option.option_id))
        .where(Response.session_id.in_(session_ids))
        .group_by(Response.session_id)
        .subquery()
    )

    result = await db.execute(select(sub.c.sid, sub.c.total, sub.c.correct))
    ratios: Dict[UUID, float] = {}
    for sid, total, correct in result.fetchall():
        total = total or 0
        correct = correct or 0
        ratios[sid] = (float(correct) / float(total)) if total else 0.0
    return ratios


async def _course_lesson_ids(db: AsyncSession, course_id: UUID) -> List[UUID]:
    res = await db.execute(
        select(CourseContent.lesson_id)
        .where(CourseContent.course_id == course_id, CourseContent.lesson_id.isnot(None))
    )
    return [row[0] for row in res.fetchall() if row[0]]


async def _course_assessment_ids(db: AsyncSession, course_id: UUID) -> List[UUID]:
    res = await db.execute(
        select(CourseContent.assessment_id)
        .where(CourseContent.course_id == course_id, CourseContent.assessment_id.isnot(None))
    )
    return [row[0] for row in res.fetchall() if row[0]]


# ------------------------
# Course & Lesson Engagement
# ------------------------

async def get_courses_overview(db: AsyncSession) -> List[CourseEngagement]:
    # Counts per course (lessons, assessments)
    cc = aliased(CourseContent)
    counts_q = (
        select(
            Course.course_id,
            Course.title,
            func.sum(case((cc.lesson_id.isnot(None), 1), else_=0)).label("lessons_count"),
            func.sum(case((cc.assessment_id.isnot(None), 1), else_=0)).label("assessments_count"),
        )
        .join(cc, cc.course_id == Course.course_id)
        .group_by(Course.course_id, Course.title)
    )
    counts = await db.execute(counts_q)
    rows = counts.fetchall()

    # Engaged users = distinct users who either viewed any lesson or attempted any assessment in the course
    out: List[CourseEngagement] = []
    for course_id, title, lessons_count, assessments_count in rows:
        lesson_ids = await _course_lesson_ids(db, course_id)
        assess_ids = await _course_assessment_ids(db, course_id)

        users_from_lessons: Set[UUID] = set()
        if lesson_ids:
            r = await db.execute(
                select(UserLessonProgress.user_id).where(UserLessonProgress.lesson_id.in_(lesson_ids))
            )
            users_from_lessons = {u[0] for u in r.fetchall()}

        users_from_assess: Set[UUID] = set()
        if assess_ids:
            r = await db.execute(
                select(UserSession.user_id).where(UserSession.assessment_id.in_(assess_ids))
            )
            users_from_assess = {u[0] for u in r.fetchall()}

        engaged = len(users_from_lessons.union(users_from_assess))
        out.append(CourseEngagement(
            course_id=course_id,
            title=title,
            lessons_count=int(lessons_count or 0),
            assessments_count=int(assessments_count or 0),
            engaged_users=int(engaged),
        ))
    return out


async def get_lessons_engagement(db: AsyncSession) -> List[LessonEngagement]:
    q = (
        select(
            Lesson.lesson_id,
            CourseContent.course_id,
            Lesson.title,
            func.count(func.distinct(UserLessonProgress.user_id)).label("unique_viewers"),
            func.sum(case((UserLessonProgress.completed.is_(True), 1), else_=0)).label("completed_viewers"),
        )
        .join(CourseContent, CourseContent.lesson_id == Lesson.lesson_id)
        .outerjoin(UserLessonProgress, UserLessonProgress.lesson_id == Lesson.lesson_id)
        .group_by(Lesson.lesson_id, CourseContent.course_id, Lesson.title)
    )
    r = await db.execute(q)
    return [
        LessonEngagement(
            lesson_id=row[0],
            course_id=row[1],
            title=row[2],
            unique_viewers=int(row[3] or 0),
            completed_viewers=int(row[4] or 0),
        )
        for row in r.fetchall()
    ]


# ------------------------
# Assessment Performance
# ------------------------

async def get_assessment_performance(db: AsyncSession, pass_threshold: float = 0.6) -> List[AssessmentPerformance]:
    # All sessions per assessment
    sess_q = (
        select(UserSession.session_id, UserSession.assessment_id)
        .where(UserSession.completed_at.isnot(None))
    )
    sess_res = await db.execute(sess_q)
    session_rows = sess_res.fetchall()

    if not session_rows:
        return []

    session_ids = [row[0] for row in session_rows]
    sess_to_assess = {row[0]: row[1] for row in session_rows}

    ratios = await _sessions_correct_ratio_map(db, session_ids)

    # Aggregate per assessment
    per_assess: Dict[UUID, Dict[str, float]] = {}
    for sid, ratio in ratios.items():
        aid = sess_to_assess.get(sid)
        if not aid:
            continue
        d = per_assess.setdefault(aid, {"attempts": 0, "passes": 0, "sum_attempts_per_user": 0})
        d["attempts"] += 1
        if ratio >= pass_threshold:
            d["passes"] += 1

    # Avg retries per user (attempts per user per assessment)
    retries_q = (
        select(UserSession.assessment_id, UserSession.user_id, func.count(UserSession.session_id).label("attempts"))
        .group_by(UserSession.assessment_id, UserSession.user_id)
    )
    retries_res = await db.execute(retries_q)
    per_user_attempts: Dict[UUID, List[int]] = {}
    for aid, uid, attempts in retries_res.fetchall():
        per_user_attempts.setdefault(aid, []).append(int(attempts))

    # Names
    names_res = await db.execute(select(Assessment.assessment_id, Assessment.name))
    names = {row[0]: row[1] for row in names_res.fetchall()}

    out: List[AssessmentPerformance] = []
    for aid, agg in per_assess.items():
        attempts = int(agg["attempts"])
        passes = int(agg["passes"])
        fails = attempts - passes
        pass_rate = round((passes / attempts) * 100.0, 2) if attempts else 0.0

        avg_retries = 1.0
        if aid in per_user_attempts and len(per_user_attempts[aid]) > 0:
            avg_retries = round(sum(per_user_attempts[aid]) / len(per_user_attempts[aid]), 2)

        out.append(AssessmentPerformance(
            assessment_id=aid,
            name=names.get(aid, "Unknown"),
            attempts=attempts,
            pass_count=passes,
            fail_count=fails,
            pass_rate=pass_rate,
            avg_retries_per_user=avg_retries
        ))
    return sorted(out, key=lambda x: x.pass_rate)  # difficult first


async def get_assessment_retries(db: AsyncSession, pass_threshold: float = 0.6) -> List[AssessmentRetryStat]:
    # Per user attempts + failed attempts
    sess_q = select(UserSession.session_id, UserSession.assessment_id, UserSession.user_id)
    sess_res = await db.execute(sess_q)
    sess = sess_res.fetchall()
    if not sess:
        return []

    session_ids = [row[0] for row in sess]
    ratios = await _sessions_correct_ratio_map(db, session_ids)

    per_assess_users: Dict[UUID, Set[UUID]] = {}
    per_assess_total_attempts: Dict[UUID, int] = {}
    per_assess_failed_attempts: Dict[UUID, int] = {}

    for sid, aid, uid in sess:
        per_assess_users.setdefault(aid, set()).add(uid)
        per_assess_total_attempts[aid] = per_assess_total_attempts.get(aid, 0) + 1
        if ratios.get(sid, 0.0) < pass_threshold:
            per_assess_failed_attempts[aid] = per_assess_failed_attempts.get(aid, 0) + 1

    names_res = await db.execute(select(Assessment.assessment_id, Assessment.name))
    names = {row[0]: row[1] for row in names_res.fetchall()}

    out: List[AssessmentRetryStat] = []
    for aid, users in per_assess_users.items():
        total = per_assess_total_attempts.get(aid, 0)
        failed = per_assess_failed_attempts.get(aid, 0)
        users_count = len(users) or 1
        out.append(AssessmentRetryStat(
            assessment_id=aid,
            name=names.get(aid, "Unknown"),
            users_attempted=users_count,
            total_attempts=total,
            avg_attempts_per_user=round(total / users_count, 2),
            failed_attempts=failed,
            fail_rate=round((failed / total) * 100.0, 2) if total else 0.0
        ))
    return sorted(out, key=lambda x: x.failed_attempts, reverse=True)


# ------------------------
# Course completion (assessment based)
# ------------------------

async def get_courses_completion(db: AsyncSession) -> List[CourseCompletionStat]:
    courses_res = await db.execute(select(Course.course_id, Course.title))
    courses = courses_res.fetchall()

    out: List[CourseCompletionStat] = []
    for course_id, title in courses:
        assess_ids = await _course_assessment_ids(db, course_id)
        assessments_in_course = len(assess_ids)
        if assessments_in_course == 0:
            out.append(CourseCompletionStat(
                course_id=course_id, title=title,
                assessments_in_course=0, users_engaged=0, avg_assessment_completion_rate=0.0
            ))
            continue

        # Users who engaged with course (sessions to these assessments)
        engaged_q = (
            select(UserSession.user_id)
            .where(UserSession.assessment_id.in_(assess_ids))
        )
        engaged_res = await db.execute(engaged_q)
        engaged_users = list({u[0] for u in engaged_res.fetchall()})
        if not engaged_users:
            out.append(CourseCompletionStat(
                course_id=course_id, title=title,
                assessments_in_course=assessments_in_course, users_engaged=0, avg_assessment_completion_rate=0.0
            ))
            continue

        # For each engaged user: fraction of assessments with a completed session
        completion_sum = 0.0
        for uid in engaged_users:
            user_done_q = (
                select(func.count(UserSession.session_id))
                .where(
                    UserSession.user_id == uid,
                    UserSession.assessment_id.in_(assess_ids),
                    UserSession.completed_at.isnot(None)
                )
            )
            done_res = await db.execute(user_done_q)
            done = int(done_res.scalar() or 0)
            completion_sum += (done / assessments_in_course)

        avg_rate_pct = round((completion_sum / len(engaged_users)) * 100.0, 2) if engaged_users else 0.0
        out.append(CourseCompletionStat(
            course_id=course_id, title=title,
            assessments_in_course=assessments_in_course,
            users_engaged=len(engaged_users),
            avg_assessment_completion_rate=avg_rate_pct
        ))

    return out



##########################################################
##########################################################
##########################################################

# --- ADD to src/services/admin_stats.py ---
from typing import Dict, Iterable, List, Set
from sqlalchemy import select, func, case, Integer
from uuid import UUID
from datetime import datetime

from src.schemas.stats import (
    UserBasic, UserRetryInfo,
    CourseUsers, LessonUsers, AssessmentUsers
)

# Adjust this import to your actual User model location if different
from src.DB.models.users import User
from src.DB.models.assessments import (
    Course, CourseContent, Lesson, Assessment,
    UserSession, UserLessonProgress, Response, Option
)

# Helper: load users (minimal contact info)
async def _load_users_basic(db, user_ids: Iterable[UUID]) -> Dict[UUID, UserBasic]:
    ids = list({u for u in user_ids if u})
    if not ids:
        return {}

    # Keep this minimal & generic: user_id + email (name optional)
    q = await db.execute(
        select(User.user_id, User.email)  # add User.full_name if you have it
        .where(User.user_id.in_(ids))
    )
    out: Dict[UUID, UserBasic] = {}
    for uid, email in q.fetchall():
        out[uid] = UserBasic(user_id=uid, email=email, name=None)
    return out

# Helper: correct-ratio per session (0..1) by Option.is_correct
async def _session_correct_ratios(db, session_ids: Iterable[UUID]) -> Dict[UUID, float]:
    sids = list({sid for sid in session_ids if sid})
    if not sids:
        return {}

    sub = (
        select(
            Response.session_id.label("sid"),
            func.count(Response.response_id).label("total"),
            func.sum(case((Option.is_correct.is_(True), 1), else_=0).cast(Integer)).label("correct"),
        )
        .select_from(Response.__table__.outerjoin(Option, Response.option_id == Option.option_id))
        .where(Response.session_id.in_(sids))
        .group_by(Response.session_id)
        .subquery()
    )
    r = await db.execute(select(sub.c.sid, sub.c.total, sub.c.correct))
    ratios: Dict[UUID, float] = {}
    for sid, total, correct in r.fetchall():
        total = total or 0
        correct = correct or 0
        ratios[sid] = (float(correct) / float(total)) if total else 0.0
    return ratios

# -------- Drill-down: Users in a Course (engaged = viewed any lesson OR attempted any assessment) --------
async def get_course_users(db, course_id: UUID) -> CourseUsers:
    # Course title
    t = await db.execute(select(Course.title).where(Course.course_id == course_id))
    title = t.scalar() or "Unknown"

    # Course lesson IDs & assessment IDs
    lessons_res = await db.execute(
        select(CourseContent.lesson_id).where(
            CourseContent.course_id == course_id,
            CourseContent.lesson_id.isnot(None)
        )
    )
    lesson_ids = [x[0] for x in lessons_res.fetchall() if x[0]]

    assess_res = await db.execute(
        select(CourseContent.assessment_id).where(
            CourseContent.course_id == course_id,
            CourseContent.assessment_id.isnot(None)
        )
    )
    assess_ids = [x[0] for x in assess_res.fetchall() if x[0]]

    users_from_lessons: Set[UUID] = set()
    if lesson_ids:
        v = await db.execute(
            select(UserLessonProgress.user_id).where(UserLessonProgress.lesson_id.in_(lesson_ids))
        )
        users_from_lessons = {row[0] for row in v.fetchall()}

    users_from_assess: Set[UUID] = set()
    if assess_ids:
        a = await db.execute(
            select(UserSession.user_id).where(UserSession.assessment_id.in_(assess_ids))
        )
        users_from_assess = {row[0] for row in a.fetchall()}

    engaged_ids = sorted(users_from_lessons.union(users_from_assess), key=lambda x: str(x))
    user_map = await _load_users_basic(db, engaged_ids)
    users = [user_map[uid] for uid in engaged_ids if uid in user_map]

    return CourseUsers(
        course_id=course_id,
        title=title,
        engaged_count=len(users),
        users=users
    )

# -------- Drill-down: Users in a Lesson (viewers & completers) --------
async def get_lesson_users(db, lesson_id: UUID) -> LessonUsers:
    # Get course_id via CourseContent and lesson title
    meta = await db.execute(
        select(CourseContent.course_id, Lesson.title)
        .join(Lesson, Lesson.lesson_id == CourseContent.lesson_id)
        .where(CourseContent.lesson_id == lesson_id)
    )
    row = meta.fetchone()
    course_id, title = (row[0], row[1]) if row else (None, "Unknown")

    v = await db.execute(
        select(UserLessonProgress.user_id, UserLessonProgress.completed)
        .where(UserLessonProgress.lesson_id == lesson_id)
    )
    viewers: Set[UUID] = set()
    completers: Set[UUID] = set()
    for uid, completed in v.fetchall():
        viewers.add(uid)
        if completed:
            completers.add(uid)

    v_map = await _load_users_basic(db, viewers)
    c_map = await _load_users_basic(db, completers)

    return LessonUsers(
        lesson_id=lesson_id,
        course_id=course_id,
        title=title,
        viewers_count=len(viewers),
        completers_count=len(completers),
        viewers=sorted([v_map[u] for u in viewers if u in v_map], key=lambda u: str(u.user_id)),
        completers=sorted([c_map[u] for u in completers if u in c_map], key=lambda u: str(u.user_id))
    )

# -------- Drill-down: Users in an Assessment (pass/fail + retries) --------
async def get_assessment_users(db, assessment_id: UUID, pass_threshold: float) -> AssessmentUsers:
    # Assessment name
    t = await db.execute(select(Assessment.name).where(Assessment.assessment_id == assessment_id))
    name = t.scalar() or "Unknown"

    # All sessions for this assessment (completed or not)
    s = await db.execute(
        select(UserSession.session_id, UserSession.user_id, UserSession.completed_at)
        .where(UserSession.assessment_id == assessment_id)
    )
    sessions = s.fetchall()
    if not sessions:
        return AssessmentUsers(
            assessment_id=assessment_id, name=name, pass_threshold=pass_threshold,
            passed_count=0, failed_count=0, passed_users=[], failed_users=[], retry_users=[]
        )

    session_ids = [sid for (sid, _, _) in sessions]
    ratios = await _session_correct_ratios(db, session_ids)

    # Aggregate per user
    per_user_attempts: Dict[UUID, int] = {}
    per_user_last: Dict[UUID, Optional[datetime]] = {}
    passed_users: Set[UUID] = set()
    failed_users: Set[UUID] = set()

    for sid, uid, completed_at in sessions:
        per_user_attempts[uid] = per_user_attempts.get(uid, 0) + 1
        last = per_user_last.get(uid)
        if completed_at and (last is None or completed_at > last):
            per_user_last[uid] = completed_at

        r = ratios.get(sid, 0.0)
        if r >= pass_threshold:
            passed_users.add(uid)
        else:
            failed_users.add(uid)

    # A user can have both pass & fail attempts; if passed at least once, treat as passed.
    failed_users -= passed_users

    # Load user contacts
    p_map = await _load_users_basic(db, passed_users)
    f_map = await _load_users_basic(db, failed_users)
    all_retry_user_ids = {uid for uid, n in per_user_attempts.items() if n > 1}
    r_map = await _load_users_basic(db, all_retry_user_ids)

    # Build retry info list
    retry_users: List[UserRetryInfo] = []
    for uid in all_retry_user_ids:
        base = r_map.get(uid)
        if not base:
            continue
        retry_users.append(UserRetryInfo(
            user_id=base.user_id, email=base.email, name=base.name,
            attempts=per_user_attempts.get(uid, 1),
            last_attempt_at=per_user_last.get(uid)
        ))
    retry_users.sort(key=lambda x: (-x.attempts, str(x.user_id)))

    return AssessmentUsers(
        assessment_id=assessment_id,
        name=name,
        pass_threshold=pass_threshold,
        passed_count=len(passed_users),
        failed_count=len(failed_users),
        passed_users=sorted([p_map[u] for u in passed_users if u in p_map], key=lambda u: str(u.user_id)),
        failed_users=sorted([f_map[u] for u in failed_users if u in f_map], key=lambda u: str(u.user_id)),
        retry_users=retry_users
    )
