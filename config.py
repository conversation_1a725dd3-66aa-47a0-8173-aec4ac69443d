from pydantic_settings import BaseSettings, SettingsConfigDict

class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        env_file=".env",     # ✅ Load from .env file
        extra='ignore'       # Ignore unknown env vars
    )

    APP_STATUS: str
    APP_VERSION: str
    LOGO_URL: str

    DATABASE_URL: str
    POOL_SIZE: int
    MAX_OVERFLOW: int
    POOL_TIMEOUT: int
    POOL_RECYCLE: int
    NULL_POOL: bool

    # Auth
    ACCESS_TOKEN_EXPIRY: int
    REFRESH_TOKEN_EXPIRY_PC: int
    REFRESH_TOKEN_EXPIRY_MO: int

    JWT_RT_SECRET: str
    JWT_AT_SECRET: str
    RATE_LIMIT: str

    # reCAPTCHA
    RECAPTCHA_SECRET_KEY: str
    RECAPTCHA_SITE_KEY: str
    RECAPTCHA_DISABLED: bool = False

    # Lockout policy
    LOCKOUT_DURATION_MINS: int
    MAX_LOGIN_ATTEMPTS: int

    ENCRYPTION_KEY: str
    
    SMTP_HOST: str 
    SMTP_PORT: int
    SMTP_USER: str
    SMTP_PASS: str
    SMTP_USE_SSL: bool      # <- important
    SMTP_STARTTLS: bool      # <- important


    
    
    # WhatsApp server
    WHTASAPP_API_KEY: str
    WHTASAPP_URL: str
    API_V1_STR: str
    CDN_HOST: str
    ENVIRONMENT: str
    BACKEND_CORS_ORIGINS: list[str]
    API_KEY: str
    API_KEY_NAME: str

# Load settings
settings = Settings()
