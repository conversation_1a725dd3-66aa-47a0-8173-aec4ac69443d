import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import func
from uuid import UUID
from datetime import datetime, timezone
from fastapi import HTTPException

from src.services.grading import calculate_result_profile
from src.utils.Error_Handling import ErrorCode
from src.DB.models.assessments import UserSession, Response, SessionProfile, Question
from src.schemas.assessments import SessionCreate, ResponseSubmit

# Create new user session (only start)
async def create_user_session(user_id:UUID, data: SessionCreate, db: AsyncSession) -> UserSession:
    session_id =uuid.uuid4()
    new_session = UserSession(
        session_id= session_id,
        user_id=user_id,
        assessment_id=data.assessment_id,
        started_at=datetime.now(tz=timezone.utc)
    )
    db.add(new_session)
    await db.commit()
    await db.refresh(new_session)
    return new_session

# Get all sessions by a user
async def get_user_sessions(user_id: UUID, db: AsyncSession):
    result = await db.execute((
        select(UserSession).where(UserSession.user_id == user_id)
    ).options(selectinload(UserSession.responses),
              selectinload(UserSession.session_profile)))
    return result.scalars().all()

async def get_user_session_by_id(user_id:UUID, session_id: UUID, db: AsyncSession):
    session_query = await db.execute(select(UserSession).where(UserSession.user_id == user_id,
                                                         UserSession.session_id == session_id))
    session = session_query.scalar_one_or_none()
    return session

async def get_user_session_response_by_id(user_id: UUID, session_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(UserSession)
        .where(
            UserSession.user_id == user_id,
            UserSession.session_id == session_id
        )
        .options(selectinload(UserSession.responses))
    )
    session = result.scalar_one_or_none()

    if not session:
        raise HTTPException(status_code=404, detail=ErrorCode.SESSION_NOT_FOUND)


    return session.responses  # Already loaded by selectinload

# Submit all answers and trigger scoring
async def submit_assessment_responses(user_id: UUID, session_id: UUID, responses: list[ResponseSubmit], db: AsyncSession):
    session = await get_user_session_by_id(user_id, session_id, db)
    if not session:
        raise HTTPException(status_code=404, detail=ErrorCode.SESSION_NOT_FOUND)

    if session and session.completed_at:
        raise HTTPException(status_code=400, detail=ErrorCode.SESSION_ALREADY_COMPLETED)

    existing_responses = await get_user_session_response_by_id(user_id, session_id, db)
    if existing_responses:
        raise HTTPException(status_code=400, detail=ErrorCode.RESPONSES_ALREADY_SUBMITTED)

    # Fetch questions for validation
    questions = await db.execute(
        select(Question).where(Question.assessment_id == session.assessment_id)
    )
    question_list = questions.scalars().all()
    answered_ids = {r.question_id for r in responses}
    expected_ids = {q.question_id for q in question_list}

    if answered_ids != expected_ids:
        raise HTTPException(status_code=400, detail=ErrorCode.NOT_ALL_QUESTIONS_ANSWERED)

    # Store all responses
    responses_db = [
        Response(
            response_id=uuid.uuid4(),
            session_id=session_id,
            question_id=r.question_id,
            option_id=r.option_id,
            value=r.value,
            answered_at=r.answered_at
        )
        for r in responses
    ]

    db.add_all(responses_db)
    session.completed_at = datetime.now(tz=timezone.utc)

    # Basic scoring logic (you can enhance this per your model)
    result_profile, confidence = await calculate_result_profile(assessment_id=session.assessment_id , responses=responses_db , db=db)
    print(f'the Result is: result_profile:{result_profile}, confidence: {confidence}')

    if not result_profile:
        raise HTTPException(status_code=500, detail=ErrorCode.COULD_NOT_CALCULATE_RESULT_PROFILE)

    profile_entry = SessionProfile(
        session_id=session_id,
        profile_id=result_profile.profile_id,
        confidence_pct=confidence
    )
    db.add(profile_entry)

    await db.commit()
    result = await get_user_session_profile_by_id(session_id, db)
    return result

# Get a session profile by id for a user_id
async def get_user_session_profile_by_id(session_id:UUID, db: AsyncSession):
    result = await db.execute(
        select(SessionProfile)
        .where(SessionProfile.session_id == session_id)
        .options(selectinload(SessionProfile.profile))
    )
    return  result.scalar_one_or_none()

async def get_user_session_response_profiles(user_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(SessionProfile)
        .join(UserSession, UserSession.session_id == SessionProfile.session_id)
        .where(UserSession.user_id == user_id)
        .options(selectinload(SessionProfile.profile))
    )
    return result.scalars().all()

async def delete_user_session(user_id: UUID, session_id: UUID, db: AsyncSession) -> None:
    result = await db.execute(select(UserSession).where(UserSession.session_id == session_id, UserSession.user_id == user_id))
    session = result.scalar_one_or_none()

    if session is None:
        raise HTTPException(
            status_code=404,
            detail=ErrorCode.SESSION_NOT_FOUND
        )
    await db.delete(session)
    await db.commit()
