import datetime
import traceback
from datetime import timed<PERSON><PERSON>

import uuid
from decimal import Decimal
from typing import List
from uuid import UUID

import httpx
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi import HTTPException, Request
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy.orm import selectinload

from src.schemas.assessments import AssessmentCreate, AssessmentUpdate, DimensionUpdate, QuestionCreate, \
    OptionCreate, OptionUpdate, QuestionUpdate, ResultProfileCreate, ResultProfileCreateList, ResultProfileUpdate, \
    ResultProfileRead
from src.utils.Error_Handling import ErrorCode
from src.utils.logger import AdvancedLogger
from config import settings
from src.DB.models.assessments import (Assessment, Dimension, Question, Option, ResultProfile,
                                                       UserSession, Response, SessionProfile)

logger = AdvancedLogger(name=__name__)


async def create_assessment(data: AssessmentCreate, db: AsyncSession):
    new_assessment = Assessment(
        assessment_id=uuid.uuid4(),
        code=data.code,
        name=data.name,
        version_int=data.version_int,
        # scoring_method=data.scoring_method,
    )
    db.add(new_assessment)

    # Create dimensions
    dimension_map = {}
    for dim in data.dimensions:
        dim_id = uuid.uuid4()
        new_dim = Dimension(
            dimension_id=dim_id,
            assessment_id=new_assessment.assessment_id,
            name=dim.name,
            code=dim.code,
            sequence=dim.sequence,
        )
        db.add(new_dim)
        dimension_map[dim.code] = dim_id

    # Create questions and options
    for que in data.questions:
        dim_id = dimension_map.get(que.dimension_code)
        if not dim_id:
            raise HTTPException(status_code=400, detail=ErrorCode.DIMENSION_NOT_FOUND)

        que_id = uuid.uuid4()
        new_que = Question(
            question_id=que_id,
            assessment_id=new_assessment.assessment_id,
            dimension_id=dim_id,
            body_md=que.body_md,
            sequence=que.sequence,
            block_label=que.block_label,
        )
        db.add(new_que)

        for opt in que.options:
            opt_id = uuid.uuid4()
            new_opt = Option(
                option_id=opt_id,
                question_id=que_id,
                label=opt.label,
                value=opt.value,
            )
            db.add(new_opt)

    await db.commit()

    # ✅ Eagerly load relationships before returning
    result = await db.execute(
        select(Assessment)
        .options(
            selectinload(Assessment.dimensions),
            selectinload(Assessment.questions)
                .selectinload(Question.options)
        )
        .where(Assessment.assessment_id == new_assessment.assessment_id)
    )
    full_assessment = result.scalar_one()

    return full_assessment

async def get_all_assessments_names(db: AsyncSession):
    result = await db.execute(
        select(Assessment)
    )
    return result.scalars().all()

async def get_all_assessments(db: AsyncSession):
    result = await db.execute(
        select(Assessment)
        .options(
            selectinload(Assessment.dimensions),
            selectinload(Assessment.questions).selectinload(Question.options),
            selectinload(Assessment.questions).selectinload(Question.dimension),
            selectinload(Assessment.result_profiles)
        )
    )
    return result.scalars().all()

async def delete_assessment_by_id(assessment_id: UUID, db: AsyncSession):
    assessment = await get_assessment_by_id(assessment_id, db)
    if not assessment:
        return None
    await db.delete(assessment)
    old_ass = assessment
    await db.commit()
    return old_ass

async def get_assessment_by_id(assessment_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(Assessment)
        .options(
            selectinload(Assessment.dimensions),
            selectinload(Assessment.questions).selectinload(Question.options),
            selectinload(Assessment.questions).selectinload(Question.dimension),
            # selectinload(Assessment.result_profiles)
        )
        .where(Assessment.assessment_id == assessment_id)
    )
    return result.scalar_one_or_none()

async def get_simple_ass_by_id(assessment_id: UUID, db: AsyncSession):
    result = await db.execute(select(Assessment).where(Assessment.assessment_id == assessment_id))
    return result.scalar_one_or_none()

async def update_assessment(assessment_id: UUID, data: AssessmentUpdate, db: AsyncSession):
    assessment = await get_assessment_by_id(assessment_id= assessment_id, db= db)
    if not assessment:
        raise HTTPException(status_code=404, detail=ErrorCode.ASSESSMENT_NOT_FOUND)

    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(assessment, k, v)

    await db.commit()
    await db.refresh(assessment)
    return assessment

async def get_dimension_by_id(dimension_id: UUID, db: AsyncSession):
    result = await db.execute(select(Dimension).where(Dimension.dimension_id == dimension_id))
    return result.scalar_one_or_none()

async def get_dimensions_by_assessment_id(assessment_id: UUID, db: AsyncSession) -> List[Dimension]:
    result = await db.execute(
        select(Dimension)
        .where(Dimension.assessment_id == assessment_id)
        .order_by(Dimension.sequence)
    )
    return result.scalars().all()

async def update_dimension(dimension_id: UUID, data: DimensionUpdate, db: AsyncSession):
    dimension = await get_dimension_by_id(dimension_id, db)
    if not dimension:
        raise HTTPException(status_code=404, detail=ErrorCode.DIMENSION_NOT_FOUND)

    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(dimension, k, v)

    await db.commit()
    await db.refresh(dimension)
    return dimension

async def delete_dimension_by_id(dimension_id: UUID, db: AsyncSession):
        dimension = await get_dimension_by_id(dimension_id, db)
        if not dimension:
            return None
        await db.delete(dimension)
        await db.commit()
        return True

async def get_question_by_id(question_id: UUID, db: AsyncSession):
    result = await db.execute(
        select(Question)
        .options(
            selectinload(Question.dimension),
            selectinload(Question.options)
        )
        .where(Question.question_id == question_id)
    )
    return result.scalars().first()

async def get_questions_by_assessment_id(assessment_id: UUID, db: AsyncSession) -> List[Question]:
    result = await db.execute(
        select(Question)
        .where(Question.assessment_id == assessment_id)
        .options(selectinload(Question.dimension),
                 selectinload(Question.options))  # Eagerly load the dimension relationship
        .order_by(Question.sequence)
    )
    return result.scalars().all()

async def get_questions_by_dimension_id(dimension_id: UUID, db: AsyncSession) -> List[Question]:
    result = await db.execute(
        select(Question)
        .options(
            selectinload(Question.options)
        )
        .where(Question.dimension_id == dimension_id)
        .order_by(Question.sequence)
    )
    return result.scalars().all()

async def update_question(question_id: UUID, data: QuestionUpdate, db: AsyncSession):
    question = await get_question_by_id(question_id, db)
    if not question:
        raise HTTPException(status_code=404, detail=ErrorCode.QUESTION_NOT_FOUND)

    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(question, k, v)

    await db.commit()
    await db.refresh(question)
    return question

async def delete_question_by_id(question_id: UUID, db: AsyncSession) -> bool:
    question = await get_question_by_id(question_id, db)
    if not question:
        return False
    await db.delete(question)
    await db.commit()
    return True

async def get_option_by_id(option_id: UUID, db: AsyncSession):
    result = await db.execute(select(Option).where(Option.option_id == option_id))
    return result.scalar_one_or_none()

async def update_option(option_id: UUID, data: OptionUpdate, db: AsyncSession):
    option = await get_option_by_id(option_id, db)
    if not option:
        raise HTTPException(status_code=404, detail=ErrorCode.OPTION_NOT_FOUND)

    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(option, k, v)

    await db.commit()
    await db.refresh(option)
    return option

async def delete_option_by_id(option_id: UUID, db: AsyncSession)-> bool:
    option = await get_option_by_id(option_id, db)
    if not option:
        return False
    await db.delete(option)
    await db.commit()
    return True

async def create_assessment_profiles_types(data: ResultProfileCreateList, db: AsyncSession)-> List[ResultProfile]:
    assessment_id= data.assessment_id
    profiles = []
    for profile in data.profiles_data:
        new_profile = ResultProfile(
            profile_id= uuid.uuid4(),
            assessment_id= assessment_id,
            code= profile.code,
            name=profile.name,
            description= profile.description,
            criteria_json= profile.criteria_json if profile.criteria_json else None
        )
        profiles.append(new_profile)
    db.add_all(profiles)
    await db.commit()
    for profile in profiles:
        await db.refresh(profile)

    return profiles


async def get_assessment_profiles_types(assessment_id:UUID , db: AsyncSession):
    result = await db.execute((
        select(ResultProfile)
    ).where(ResultProfile.assessment_id ==assessment_id ))
    return result.scalars().all()

async def get_assessment_profile_type_by_id(profile_id:UUID ,assessment_id:UUID , db: AsyncSession):
    result = await db.execute((
        select(ResultProfile)
    ).where(ResultProfile.assessment_id ==assessment_id, ResultProfile.profile_id ==  profile_id))
    return result.scalar_one_or_none

async def update_assessment_profile_type_by_id(profile_id:UUID ,assessment_id:UUID, data:ResultProfileUpdate , db: AsyncSession):
    assessment_profile = await get_assessment_profile_type_by_id(profile_id=profile_id, assessment_id=assessment_id, db=db)
    if not assessment_profile:
        raise HTTPException(status_code=404, detail=ErrorCode.OPTION_NOT_FOUND)

    for k, v in data.model_dump(exclude_unset=True).items():
        setattr(assessment_profile, k, v)

    await db.commit()
    await db.refresh(assessment_profile)
    return assessment_profile

async def delete_assessment_profile_type_by_id(profile_id:UUID ,assessment_id:UUID, db: AsyncSession):
    assessment_profile = await get_assessment_profile_type_by_id(profile_id=profile_id, assessment_id=assessment_id, db=db)
    if not assessment_profile:
        return False
    await db.delete(assessment_profile)
    await db.commit()
    return True
