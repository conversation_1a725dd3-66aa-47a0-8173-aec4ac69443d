# src/routes/user_stats_router.py
from fastapi import APIRouter, Request, Depends, Query
from typing import List
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.handle_router_exceptions import handle_router_exceptions

from src.schemas.stats import CourseProgressRead, UserAssessmentStat, UserLessonStat
from src.services.user_stats import (
    get_all_courses_progress, get_course_progress,
    get_user_assessment_stats, get_user_lessons_stats
)

user_stats_router = APIRouter(
    prefix="/user/stats",
    tags=["User Statistics"],
    dependencies=[Depends(get_current_user_upgrade)]
)


@user_stats_router.get("/courses", response_model=List[CourseProgressRead])
@handle_router_exceptions
async def my_courses_progress_ep(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    return await get_all_courses_progress(db, user_id)


@user_stats_router.get("/courses/{course_id}/progress", response_model=CourseProgressRead)
@handle_router_exceptions
async def my_course_progress_ep(request: Request, user_id: CurrentUserUpgrade, course_id, db: SessionDep):
    return await get_course_progress(db, user_id, course_id)


@user_stats_router.get("/assessments", response_model=List[UserAssessmentStat])
@handle_router_exceptions
async def my_assessments_stats_ep(
    request: Request,
    user_id: CurrentUserUpgrade,
    db: SessionDep,
    pass_threshold: float = Query(0.6, ge=0.0, le=1.0)
):
    return await get_user_assessment_stats(db, user_id, pass_threshold=pass_threshold)


@user_stats_router.get("/lessons", response_model=List[UserLessonStat])
@handle_router_exceptions
async def my_lessons_stats_ep(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    return await get_user_lessons_stats(db, user_id)
