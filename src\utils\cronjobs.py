from apscheduler.jobstores.base import JobLookupError
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import logging

logger = logging.getLogger(__name__)
scheduler = AsyncIOScheduler()




def start_cron_jobs():
        try:
            scheduler.add_job(
                func="",
                trigger=IntervalTrigger(hours=24),  # every 24 hours from startup time
                name="",
                replace_existing=True,
            )
            scheduler.start()
            logger.info("Cron job scheduled to run every 24 hours.")
        except JobLookupError as e:
            logger.error(f"Failed to schedule job: {e}")
