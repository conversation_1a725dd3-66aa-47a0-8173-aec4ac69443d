from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

class SecurityMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)
        
        # Add security headers (similar to Helmet in Express.js)
        response.headers["X-Frame-Options"] = "DENY"  # Prevent clickjacking
        # response.headers["Content-Security-Policy"] = "default-src 'self'"  # Restrict content sources
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"  # Enforce HTTPS
        response.headers["Permissions-Policy"] = "camera=(), microphone=(), geolocation=()"  # Restrict permissions
        
        return response