# src/schemas/stats.py
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List, Dict
from uuid import UUID
from datetime import datetime


# -------- Admin: Courses / Lessons Engagement --------

class CourseEngagement(BaseModel):
    course_id: UUID
    title: str
    lessons_count: int
    assessments_count: int
    engaged_users: int  # distinct users who viewed lessons OR attempted assessments

class LessonEngagement(BaseModel):
    lesson_id: UUID
    course_id: UUID
    title: str
    unique_viewers: int
    completed_viewers: int


# -------- Admin: Assessments Performance --------

class AssessmentPerformance(BaseModel):
    assessment_id: UUID
    name: str
    attempts: int
    pass_count: int
    fail_count: int
    pass_rate: float
    avg_retries_per_user: float

class AssessmentRetryStat(BaseModel):
    assessment_id: UUID
    name: str
    users_attempted: int
    total_attempts: int
    avg_attempts_per_user: float
    failed_attempts: int
    fail_rate: float


# -------- Admin: Course Completion (assessments) --------

class CourseCompletionStat(BaseModel):
    course_id: UUID
    title: str
    assessments_in_course: int
    users_engaged: int
    avg_assessment_completion_rate: float  # averaged across engaged users (0..100)


# -------- User: Personal Progress --------

class CourseProgressRead(BaseModel):
    course_id: UUID
    title: str
    total_contents: int
    completed_lessons: int
    completed_assessments: int
    progress_pct: float  # 0..100

class UserAssessmentStat(BaseModel):
    assessment_id: UUID
    name: str
    attempts: int
    last_attempt_at: Optional[datetime]
    best_correct_ratio: Optional[float]  # 0..1
    passed_once: bool

class UserLessonStat(BaseModel):
    lesson_id: UUID
    title: str
    viewed_at: Optional[datetime]
    completed: bool


# --- User Basics used in drill-down responses ---
class UserBasic(BaseModel):
    user_id: UUID
    email: EmailStr
    name: Optional[str] = None  # leave None if you don't store names

# For retries listing (includes attempts & last attempt)
class UserRetryInfo(UserBasic):
    attempts: int
    last_attempt_at: Optional[datetime] = None

# --- Drill-down responses ---

class CourseUsers(BaseModel):
    course_id: UUID
    title: str
    engaged_count: int
    users: List[UserBasic]

class LessonUsers(BaseModel):
    lesson_id: UUID
    course_id: UUID
    title: str
    viewers_count: int
    completers_count: int
    viewers: List[UserBasic]
    completers: List[UserBasic]

class AssessmentUsers(BaseModel):
    assessment_id: UUID
    name: str
    pass_threshold: float
    passed_count: int
    failed_count: int
    passed_users: List[UserBasic]
    failed_users: List[UserBasic]
    # Users with multiple attempts (sorted by attempts desc)
    retry_users: List[UserRetryInfo]
