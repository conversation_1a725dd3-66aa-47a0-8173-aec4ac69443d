"""Add dimension enums for code and name fields

Revision ID: 9ac70e113ace
Revises: 97ed84539616
Create Date: 2025-09-20 17:45:26.476124

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '9ac70e113ace'
down_revision: Union[str, None] = '97ed84539616'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create the enum types first
    dimensioncode_enum = sa.Enum('TF', 'SC', 'MC', name='dimensioncode')
    dimensiontype_enum = sa.Enum('TRUE_FALSE', 'SINGLE_CHOICE', 'MULTIPLE_CHOICE', name='dimensiontype')

    dimensioncode_enum.create(op.get_bind())
    dimensiontype_enum.create(op.get_bind())

    # Update existing data to match enum values (if any exists)
    # This assumes existing data might need to be converted to the new enum values
    # You may need to adjust this based on your existing data

    # Convert code column to enum
    op.alter_column('dimensions', 'code',
               existing_type=sa.VARCHAR(),
               type_=dimensioncode_enum,
               existing_nullable=False,
               postgresql_using='code::dimensioncode')

    # Convert name column to enum
    op.alter_column('dimensions', 'name',
               existing_type=sa.VARCHAR(),
               type_=dimensiontype_enum,
               existing_nullable=False,
               postgresql_using='name::dimensiontype')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('dimensions', 'name',
               existing_type=sa.Enum('TRUE_FALSE', 'SINGLE_CHOICE', 'MULTIPLE_CHOICE', name='dimensiontype'),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('dimensions', 'code',
               existing_type=sa.Enum('TF', 'SC', 'MC', name='dimensioncode'),
               type_=sa.VARCHAR(),
               existing_nullable=False)

    # Drop the enum types
    sa.Enum(name='dimensiontype').drop(op.get_bind())
    sa.Enum(name='dimensioncode').drop(op.get_bind())
    # ### end Alembic commands ###
