from datetime import timedelta, datetime, timezone
import random

import httpx

from user_agents import parse
from fastapi import HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Iterable
import string
import secrets

from src.utils.user_utils import generate_pass_hash
from src.DB.models.users import User
from src.schemas import auth as auth_schema
from config import settings
from src.utils.Error_Handling import ErrorCode

async def create_verification_code_general(user: User, db: AsyncSession) -> bool:
    try:
        random_number = random.randint(100000, 999999)
        user.auth.verification_code = str(random_number)
        user.auth.verification_code_exp = datetime.now(
            tz=timezone.utc) + timedelta(minutes=20)
        await db.commit()
        await db.refresh(user)
        return True
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=f"{ErrorCode.UNEXPECTED_ERROR}: {str(e)}")

async def check_verification_code_general(user: User, verification_code: str) -> None:
    if not verification_code:
        raise HTTPException(
            status_code=400, detail=ErrorCode.INVALID_VERIFICATION_CODE)
    if user.auth.verification_code != verification_code:
        raise HTTPException(
            status_code=400, detail=ErrorCode.INVALID_VERIFICATION_CODE)
    if user.auth.verification_code_exp < datetime.now(tz=timezone.utc):
        raise HTTPException(
            status_code=400, detail=ErrorCode.EXPIRED_VERIFICATION_CODE)

async def verify_recaptcha(token: str, remote_ip: Optional[str] = None):
    """Verify reCAPTCHA token with Google's API."""
    if settings.RECAPTCHA_DISABLED:
        return True
    url = "https://www.google.com/recaptcha/api/siteverify"
    data = {
        "secret": settings.RECAPTCHA_SECRET_KEY,
        "response": token,
        "remoteip": remote_ip
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(url, data=data)
        result = response.json()

        if not result.get("success", False):
            raise HTTPException(
                status_code=400, detail=ErrorCode.RECAPTCHA_FAILED)
        return True

async def get_device(request: Request):
    async with httpx.AsyncClient() as client:
        response = await client.get("https://api.ipify.org")

    # Get IP information
    client_ip = request.client.host  # Local IP, e.g., ***********
    public_ip = response.text

    # Device detection
    user_agent_string = request.headers.get("User-Agent", "Unknown")
    user_agent = parse(user_agent_string)
    device_type = "MobilePhone" if user_agent.is_mobile else "PC" if user_agent.is_pc else "Other/Unknown"

    # Return response with IP
    return {
        "ip": client_ip,  # Local IP
        "device_type": device_type,
        "public_ip": public_ip  # Public IP, e.g., **************
    }

async def get_public_ip():
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("https://api.ipify.org")
            return response.text
    except httpx.RequestError as e:
        print(f"Failed to get public IP: {e}")
        return "Unknown"

async def new_verification_code(user: User, db: AsyncSession) -> bool:
    new_code = await create_verification_code_general(user, db)
    return new_code

async def user_verification_code(user: User, user_data: auth_schema.PasswordResetRequest, db: AsyncSession):
    try:
        await check_verification_code_general(user, user_data.verificationCode)

        if user_data.new_password != user_data.confirm_password:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail=ErrorCode.PASSWORDS_DONT_MATCH)

        user.auth.email_confirmed = True
        user.auth.hashed_password = generate_pass_hash(
            user_data.new_password)
        user.auth.verification_code = None
        user.auth.verification_code_exp = None

        await db.commit()
        await db.refresh(user)

    except HTTPException as e:
        await db.rollback()
        raise e
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=f"{ErrorCode.UNEXPECTED_ERROR}: {str(e)}")

SAFE_SYMBOLS = "!@#$%^&*()-_=+[]{}:,.?~"

def generate_complex_password(length: int = 12,
                              symbols: Iterable[str] = SAFE_SYMBOLS) -> str:
    if length < 6:
        raise ValueError("Password length should be at least 6 characters.")

    upper = string.ascii_uppercase
    lower = string.ascii_lowercase
    digits = string.digits
    symbs = "".join(symbols)

    password = [
        secrets.choice(upper),
        secrets.choice(lower),
        secrets.choice(digits),
        secrets.choice(symbs),
    ]

    all_chars = upper + lower + digits + symbs
    password += [secrets.choice(all_chars) for _ in range(length - 4)]

    secrets.SystemRandom().shuffle(password)
    return "".join(password)
