from pydantic import BaseModel
from uuid import UUID
from typing import Optional
from datetime import datetime

class WhatsAppSessionCreate(BaseModel):
    session_id: str
    phone_number: str

class WhatsAppSessionOut(BaseModel):
    _id: UUID
    session_id: str
    user_id: UUID
    phone_number: str
    qr_code: Optional[str] = None
    status: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True