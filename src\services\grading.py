from typing import List, Any, Coroutine
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import func
from uuid import UUID
from datetime import datetime, timezone
from fastapi import HTT<PERSON><PERSON>x<PERSON>

from src.DB.enums import AssessmentType
from src.services.assessments import get_simple_ass_by_id
from src.utils.Error_Handling import ErrorCode
from src.DB.models.assessments import UserSession, Response, SessionProfile, Question, Option, ResultProfile
from src.schemas.assessments import SessionCreate, ResponseSubmit, SessionResult, ResultProfileRead


async def calculate_result_profile(assessment_id: UUID ,responses: List[Response],  db: AsyncSession):
    assessment = await get_simple_ass_by_id(assessment_id, db)

    if assessment.code == AssessmentType.MBTI:
        return await assign_mbti_profile(assessment_id, responses, db)
    elif assessment.code == AssessmentType.DISC:
        pass
    elif assessment.code == AssessmentType.HEXACO:
        return await assign_hexaco_profile(assessment_id, responses, db)
    elif assessment.code == AssessmentType.PF16:
        pass
    elif assessment.code == AssessmentType.KEIRSEY:
        pass
    elif assessment.code == AssessmentType.RORSCHACH:
        pass
    return None



async def assign_mbti_profile(
    assessment_id: UUID,
    responses: List[Response],
    db: AsyncSession
) -> tuple[Any | None, int]:
    # Step 1: Score MBTI result
    scores = {
        "I": 0, "E": 0,
        "S": 0, "N": 0,
        "T": 0, "F": 0,
        "J": 0, "P": 0
    }

    for response in responses:
        value = (response.value or "").strip().upper()
        if value in scores:
            scores[value] += 1

    mbti_code = ""
    mbti_code += "I" if scores["I"] > scores["E"] else "E"
    mbti_code += "S" if scores["S"] > scores["N"] else "N"
    mbti_code += "T" if scores["T"] > scores["F"] else "F"
    mbti_code += "J" if scores["J"] > scores["P"] else "P"

    # Step 2: Fetch matching ResultProfile
    result_query = await db.execute(
        select(ResultProfile)
        .where(ResultProfile.assessment_id == assessment_id)
        .where(ResultProfile.code == mbti_code)
    )
    result_profile = result_query.scalar_one_or_none()

    if not result_profile:
        raise ValueError(f"No ResultProfile found for MBTI type '{mbti_code}' and assessment {assessment_id}")

    confidence = 100

    return result_profile, confidence


async def assign_hexaco_profile(
    assessment_id: UUID,
    responses: List[Response],
    db: AsyncSession
) -> tuple[Any | None, int]:
    # 1. Fetch all questions with dimension relationship
    question_ids = [r.question_id for r in responses]
    questions_query = await db.execute(
        select(Question).where(Question.question_id.in_(question_ids))
    )
    questions = questions_query.scalars().all()
    question_map = {q.question_id: q for q in questions}

    # 2. Prepare trait score bins
    trait_scores = {
        "H": [],
        "E": [],
        "X": [],
        "A": [],
        "C": [],
        "O": [],
    }

    # 3. Aggregate response values by dimension code
    for response in responses:
        question = question_map.get(response.question_id)
        if not question or not question.dimension:
            continue

        dimension_code = (question.dimension.code or "").upper()
        try:
            value = int(response.value)
        except (TypeError, ValueError):
            continue

        if dimension_code in trait_scores:
            trait_scores[dimension_code].append(value)

    # 4. Average per trait
    trait_averages = {
        trait: (sum(scores) / len(scores)) if scores else 0
        for trait, scores in trait_scores.items()
    }

    # 5. Dominant trait = highest average
    dominant_trait = max(trait_averages.items(), key=lambda x: x[1])[0]
    print(dominant_trait)

    # 6. Fetch matching result profile
    result_query = await db.execute(
        select(ResultProfile)
        .where(ResultProfile.assessment_id == assessment_id)
        .where(ResultProfile.code == dominant_trait)
    )
    result_profile = result_query.scalar_one_or_none()

    if not result_profile:
        raise ValueError(f"ResultProfile not found for HEXACO trait '{dominant_trait}'")

    raw_confidence = (trait_averages[dominant_trait] / 5.0) * 100
    print("Raw confidence (before int cast):", raw_confidence)
    confidence = int(raw_confidence)
    print("Trait averages:", trait_averages)
    print("Dominant trait:", dominant_trait)
    print("Dominant trait value:", trait_averages[dominant_trait])

    print(f'the Result is: result_profile:{result_profile}, confidence: {confidence}')
    return result_profile, confidence
