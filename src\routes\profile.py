import src.schemas.user
from src.services import auth as auth_services
from src.schemas.user import Country
from src.services.general import get_country_by_id
from src.utils.Error_Handling import ErrorCode
from src.utils.logger import AdvancedLogger
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse
from fastapi import APIRouter, HTTPException, status, UploadFile, Request

from src.schemas import auth as user_schema
from src.services import auth as auth_services
from src.routes.deps import SessionDep, CurrentUserUpgrade
from src.utils.Error_Handling import ErrorCode


logger = AdvancedLogger(name=__name__)

profile_router = APIRouter(
    prefix='/me',
    tags=["Profile"],
)

# get user profile
@profile_router.get(
    "/",
    response_model=src.schemas.user.UserRead,
    responses={
        200: {"model": src.schemas.user.UserRead, "description": "User profile retrieved successfully"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def get_user_profile(request: Request, user_id: CurrentUserUpgrade, db: SessionDep) -> src.schemas.user.UserRead:
    """ This route is used to get user's profile (for Login Users) """
    user = await auth_services.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    user_response = src.schemas.user.UserRead.model_validate(user)
    if user.country_id:
        country = await get_country_by_id(db, user.country_id)
        if country:
            user_response.country = Country.model_validate(country)
    return user_response

@profile_router.put(
    "/email",
    response_model=user_schema.ResponseMessage,
    responses={
        202: {"model": user_schema.ResponseMessage, "description": "Email updated successfully"},
        400: {"model": ErrorResponse, "description": "Invalid email or verification code"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_202_ACCEPTED
)
@handle_router_exceptions
async def update_user_email(request: Request, update_data: user_schema.EmailUpdateRequest, db: SessionDep,
                          user_id: CurrentUserUpgrade):
    """ This route is used to update user's email (for Login Users) """
    user = await auth_services.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    if user.email != update_data.email:
        raise HTTPException(status_code=400, detail=ErrorCode.EMAIL_ERROR)
    await auth_services.update_email(user, update_data, db)
    update_data_to_email = {
        "email": update_data.email
    }
    await auth_services.update_profile(user, update_data_to_email, db)
    return user_schema.ResponseMessage(message="Email Changed successfully")

@profile_router.put(
    "/change-password",
    response_model=user_schema.ResponseMessage,
    responses={
        200: {"model": user_schema.ResponseMessage, "description": "Password changed successfully"},
        400: {"model": ErrorResponse, "description": "Invalid old password or passwords don't match"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def change_password_for_user(request: Request, user_data: user_schema.NewPassword, db: SessionDep,
                                 user_id: CurrentUserUpgrade):
    """ This Router Used For Changing Password (for Login Users)"""
    user = await auth_services.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    await auth_services.user_change_password(user, user_data, db)
    return user_schema.ResponseMessage(message="Password changed successfully")

@profile_router.delete(
    "/deactivate",
    response_model=user_schema.ResponseMessage,
    responses={
        200: {"model": user_schema.ResponseMessage, "description": "Account deactivated successfully"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def update_user_status(request: Request, db: SessionDep, user_id: CurrentUserUpgrade):
    """ This Router Used For Deactivate the Account (for Login Users)"""
    user = await auth_services.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    await auth_services.update_status(user, db)
    return user_schema.ResponseMessage(message="The User Deactivated successfully")

@profile_router.put(
    "/edit-profile",
    response_model=user_schema.ResponseMessage,
    responses={
        200: {"model": user_schema.ResponseMessage, "description": "Profile updated successfully"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def update_user_status(request: Request, db: SessionDep, user_id: CurrentUserUpgrade, update_data: user_schema.UserUpdate):
    """ This Router Used For Update the Account (for Login Users)"""
    user = await auth_services.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    await auth_services.update_profile(user, update_data, db)
    return user_schema.ResponseMessage(message="The User Updated successfully")

@profile_router.post(
    "/upload_profile_picture",
    responses={
        200: {"model": dict, "description": "Profile picture uploaded successfully", "content": {
            "application/json": {
                "example": {"profile_picture_url": "https://cdn.example.com/profile_pictures/uuid.jpg"}
            }
        }},
        400: {"model": ErrorResponse, "description": "No profile picture uploaded"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid or expired token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def upload_profile_picture(request: Request, db: SessionDep, user_id: CurrentUserUpgrade, profile_picture: UploadFile):
    """Uploads a profile picture and returns the URL"""
    if not profile_picture:
        raise HTTPException(status_code=400, detail="No profile picture uploaded")

    profile_picture_url = await auth_services.upload_profile_picture_helper(
        db=db, user_id=user_id, profile_picture=profile_picture)

    return {"profile_picture_url": profile_picture_url}