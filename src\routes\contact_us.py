from typing import List, Annotated
from uuid import UUID
from fastapi import Request
from src.DB.enums import User<PERSON>ole
from src.schemas.user import ContactUsRead
from src.services.contact_us import get_submission, get_all_submissions, add_contact_submission, \
    reply_message
from src.services.auth import  get_user_by_id
from src.utils.Error_Handling import ErrorCode
from src.utils.logger import AdvancedLogger
from src.schemas import user as user_schema
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse
from fastapi import APIRouter, HTTPException, Depends, status


logger = AdvancedLogger(name=__name__)

contact_router = APIRouter(
    prefix='/contact-us',
    tags=["Contact Us"]
)

@contact_router.post("/submissions", response_model=user_schema.ContactUsRead, status_code=status.HTTP_200_OK)
@handle_router_exceptions
async def create_submission(request: Request, user_id: CurrentUserUpgrade ,db:SessionDep, data: user_schema.ContactUsCreate ):
    user = await get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND.value)
    contact_data = await add_contact_submission(user, db,data)
    return ContactUsRead.model_validate(contact_data)

@contact_router.post("/reply", status_code=status.HTTP_200_OK)
@handle_router_exceptions
async def response(request: Request,user_id: CurrentUserUpgrade, data: user_schema.ContactUsResponse, db: SessionDep):
    user = await get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND.value)
    if UserRole.ADMIN not in user.roles:
        raise HTTPException(status_code=403, detail=ErrorCode.NOT_ADMIN.value)
    db_submission = await get_submission(sub_id=data.submission_id, db=db)
    if not db_submission:
        raise HTTPException(status_code=404, detail=ErrorCode.INVALID_SUBMISSION_ID.value)
    send_response = await reply_message(submission=db_submission, response_data=data, db=db)
    if not send_response:
        raise HTTPException(status_code=500, detail=ErrorCode.FAILED_TO_SEND_RESPONSE.value)
    return None

@contact_router.get("/submissions",
                    response_model=List[user_schema.ContactUsRead],
                    status_code=status.HTTP_200_OK,
                    dependencies=[Depends(get_current_user_upgrade)])
@handle_router_exceptions
async def get_submissions(request: Request, db:SessionDep):
    contacts_data = await get_all_submissions(db)
    return [ContactUsRead.model_validate(contact) for contact in contacts_data]

@contact_router.get("/submissions/{submission_id}",
                    response_model=user_schema.ContactUsRead,
                    status_code=status.HTTP_200_OK, dependencies=[Depends(get_current_user_upgrade)])
@handle_router_exceptions
async def get_submission_by_id(request: Request, submission_id: UUID, db:SessionDep):
    contact_data = await get_submission(sub_id=submission_id, db=db)
    return ContactUsRead.model_validate(contact_data)
