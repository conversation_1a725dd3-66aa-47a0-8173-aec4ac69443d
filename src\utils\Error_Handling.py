from enum import Enum

class ErrorCode(str, Enum):
    # ──────────────── AUTH & <PERSON><PERSON><PERSON><PERSON> ──────────────── #
    ACCOUNT_LOCKED = "account_locked"
    ACCOUNT_LOCKED_MINUTES = "account_locked_minutes"
    EMAIL_ERROR = "email_does_not_belong_to_account"
    EMAIL_NOT_CONFIRM = "email_not_confirmed"
    EXIST_EMAIL = "email_already_registered"
    FAILED_TO_SEND_EMAIL = "failed_to_send_email"
    FAILED_TO_Update_EMAIL = "failed_to_update_email"
    INVALID_CREDENTIALS = "invalid_credentials"
    INVALID_FACEBOOK_TOKEN = "invalid_Facebook_token"
    INVALID_GOOGLE_TOKEN = "invalid_Google_token"
    INVALID_VERIFICATION_CODE = "invalid_verification_code"
    LOGIN_INVALID_ERROR = "login_invalid_error"
    LOGIN_INVALID_password_ERROR = "login_invalid_password_error"
    MISSING_USER_INFO = "missing_user_info"
    NOT_ADMIN = "ADMIN_ONLY"
    OLD_PASSWORD_INCORRECT = "old_password_incorrect"
    OLD_USER = "old_user"
    PASSWORDS_DONT_MATCH = "passwords_dont_match"
    PASSWORD_CHANGE_ERROR = "password_change_error"
    RECAPTCHA_FAILED = "recaptcha_failed"
    
    # ──────────────── TOKEN ERRORS ──────────────── #
    ACCESS_TOKEN_EXPIRED = "access_token_expired"
    ACCESS_TOKEN_NOT_EXPIRED = "access_token_not_expired"
    ERROR_DECODING_TOKEN = "Error_decoding_token"
    INVALID_ACCESS_TOKEN = "invalid_access_token"
    INVALID_AUTHORIZATION_HEADER = "invalid_authorization_header"
    INVALID_REFRESH_TOKEN = "invalid_refresh_token"
    NO_REFRESH_TOKEN_PROVIDED = "no_refresh_token_provided"
    REFRESH_TOKEN_EXPIRED = "refresh_token_expired"

    # ──────────────── USER & SYNC ERRORS ──────────────── #
    ERROR_SYNCING_USER = "error_syncing_user"
    ERROR_SYNCING_USERS_MICROSERVICES = "error_syncing_users_with_microservices"
    INTERNAL_ERROR_SYNCING_USER_MICROSERVICES = "internal_error_syncing_user_with_microservices"
    INVALID_JSON_RESPONSE_MICROSERVICE = "invalid_json_response_from_microservice"
    USER_NOT_FOUND = "user_not_found"
    THIS_USER_NOT_FOUND = "this_user_not_found"

    # ──────────────── ASSESSMENT & QUESTIONS ──────────────── #
    ALLOW_TO_CONTENT_MANAGER_ONLY = "allow_to_content_manger_only"
    ASSESSMENT_NOT_FOUND = "assessment_not_found"
    ASSESSMENT_PROFILE_NOT_FOUND = "assessment_profile_not_found"
    ASSESSMENT_PROFILES_NOT_FOUND = "assessment_profiles_not_found"
    ASSESSMENT_TYPES_NOT_CREATED = "assessment_types_not_created"
    COULD_NOT_CALCULATE_RESULT_PROFILE = "could_not_calculate_result_profile"
    DIMENSION_NOT_FOUND = "dimension_not_found"
    NOT_ALL_QUESTIONS_ANSWERED = "not_all_questions_answered"
    OPTION_NOT_FOUND = "option_not_found"
    OPTION_SCORE_NOT_FOUND = "option_score_not_found"
    QUESTION_NOT_FOUND = "question_not_found"

    # ──────────────── SESSION ERRORS ──────────────── #
    INVALID_SUBMISSION_ID = "invalid_submission_id"
    RESPONSES_ALREADY_SUBMITTED = "responses_already_submitted"
    SESSION_ALREADY_COMPLETED = "session_already_completed"
    SESSION_ALREADY_EXISTS = "session_already_exists"
    SESSION_NOT_FOUND = "session_not_found"
    THIS_SESSION_NOT_BELONG_TO_THIS_USER = "this_session_not_belong_to_this_user"
    NO_USER_SESSIONS = "no_user_sessions"

    # ──────────────── API / SERVER ERRORS ──────────────── #
    API_CONNECT_FAILED = "api_connect_failed"
    API_CREATE_FAILED = "api_create_failed"
    API_DELETE_FAILED = "api_delete_failed"
    API_DISCONNECT_FAILED = "api_disconnect_failed"
    API_QR_FAILED = "api_qr_failed"
    API_STATUS_FAILED = "api_status_failed"
    DB_ERROR = "database_error"
    ERROR_ON_DATABASE_CONNECTION = "error_in_database_connection"
    FAILED_TO_SEND_RESPONSE = "failed_to_send_response"
    INTERNAL_SERVER_ERROR = "internal_server_error"
    QR_NOT_AVAILABLE = "qr_not_available"
    SET_ACTIVE_FAILED = "set_active_failed"
    UNEXPECTED_ERROR = "unexpected_error"
    
    
    def __str__(self):
        return self.value

    def to_str(self):
        return self.value
