import smtplib
import socket
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from email.header import Header
from email.utils import formataddr
from email_validator import validate_email, EmailNotValidError

from config import settings
from src.utils.Email.identity_templates.email_templates import (
    email_verification_template,
    password_reset_verification_template,
    email_verification_template_for_admin_user,
)
from src.schemas.user import UserRead
from src.utils.logger import AdvancedLogger

# Initialize logger at module level
logger = AdvancedLogger(__name__)


class Email:
    def __init__(self, user: UserRead):
        self.user = user
        # Configurable branding/sender info with safe fallbacks
        self.from_name = getattr(settings, "EMAIL_FROM_NAME", "Mr. <PERSON>")
        self.from_email = getattr(settings, "EMAIL_FROM_ADDRESS", settings.SMTP_USER)
        self.envelope_from = getattr(settings, "SMTP_ENVELOPE_FROM", self.from_email)
        self.reply_to = getattr(settings, "EMAIL_REPLY_TO", None)
        self.brand = getattr(settings, "BRAND_NAME", self.from_name)

    def _send_email(self, subject: str, body: str) -> bool:
        required_settings = [
            settings.SMTP_USER,
            settings.SMTP_PASS,
            settings.SMTP_HOST,
            settings.SMTP_PORT,
        ]
        if not all(required_settings):
            logger.error("Missing required SMTP configuration settings")
            return False

        # Validate email address
        try:
            socket.setdefaulttimeout(5)
            valid = validate_email(str(self.user.email), check_deliverability=True)
            to_email = valid.normalized
        except EmailNotValidError as e:
            logger.error(f"Invalid email address {self.user.email}: {str(e)}")
            return False
        except socket.timeout:
            logger.warning(
                f"DNS timeout for {self.user.email}, proceeding without deliverability check"
            )
            valid = validate_email(str(self.user.email), check_deliverability=False)
            to_email = valid.normalized

        # Build the message with proper display name + address
        msg = MIMEMultipart()
        msg["From"] = formataddr((str(Header(self.from_name, "utf-8")), self.from_email))
        msg["To"] = to_email
        msg["Subject"] = str(Header(subject, "utf-8"))
        if self.reply_to:
            msg["Reply-To"] = self.reply_to
        msg.attach(MIMEText(body, "html", "utf-8"))

        # Send email with correct SSL/STARTTLS handling
        try:
            logger.debug(
                f"SMTP settings: server={settings.SMTP_HOST}, port={settings.SMTP_PORT}, "
                f"from={self.from_email}"
            )

            use_ssl = getattr(settings, "SMTP_USE_SSL", False)
            use_starttls = getattr(settings, "SMTP_STARTTLS", True)

            if use_ssl:
                server = smtplib.SMTP_SSL(
                    settings.SMTP_HOST, settings.SMTP_PORT, timeout=60
                )
            else:
                server = smtplib.SMTP(
                    settings.SMTP_HOST, settings.SMTP_PORT, timeout=60
                )

            with server:
                server.ehlo()
                if (not use_ssl) and use_starttls:
                    server.starttls()
                    server.ehlo()
                server.login(settings.SMTP_USER, settings.SMTP_PASS)
                # Use envelope_from explicitly for bounce handling
                server.sendmail(self.envelope_from, [to_email], msg.as_string())

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except smtplib.SMTPException as e:
            logger.error(f"SMTP error sending email to {self.user.email}: {str(e)}")
            return False
        except socket.error as e:
            logger.error(f"Network error sending email to {self.user.email}: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending email to {self.user.email}: {str(e)}")
            return False

    # ---------- Public methods with better subjects ----------

    def send_registration_email(self, email_verification_code: str) -> bool:
        return self._send_email(
            subject=f"{self.brand} – Confirm your registration",
            body=email_verification_template(self.user.name, email_verification_code),
        )

    def send_registration_email_for_admin_user(self, password: str) -> bool:
        return self._send_email(
            subject=f"{self.brand} – Your admin account",
            body=email_verification_template_for_admin_user(self.user.name, password),
        )

    def send_password_reset_email(self, token: str) -> bool:
        return self._send_email(
            subject=f"{self.brand} – Reset your password",
            body=password_reset_verification_template(self.user.name, token),
        )
