"""002_add_UserLessonProgress

Revision ID: 97ed84539616
Revises: 273db19eeda3
Create Date: 2025-09-13 20:08:12.642136

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '97ed84539616'
down_revision: Union[str, None] = '273db19eeda3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_lesson_progress',
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('lesson_id', sa.UUID(), nullable=False),
    sa.Column('viewed_at', sa.DateTime(timezone=True), nullable=True),
    sa.<PERSON>umn('completed', sa.<PERSON>(), nullable=True),
    sa.ForeignKeyConstraint(['lesson_id'], ['lessons.lesson_id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.user_id'], ),
    sa.PrimaryKeyConstraint('user_id', 'lesson_id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_lesson_progress')
    # ### end Alembic commands ###
