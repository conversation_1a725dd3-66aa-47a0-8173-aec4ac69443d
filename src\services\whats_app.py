from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from src.DB.models.users import WhatsAppSession
from fastapi import HTTPException
from src.schemas.whats_app import WhatsAppSessionCreate, WhatsAppSessionOut
from src.utils.whats_app import WhatsAppApiClient
from uuid import UUID
from typing import Optional, List
from src.utils.Error_Handling import ErrorCode

def get_wa_api():
    return WhatsAppApiClient()

async def create_whatsapp_session(user_id: UUID, db: AsyncSession, data: WhatsAppSessionCreate) -> WhatsAppSessionOut:
    wa_api = get_wa_api()
    try:
        result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == data.session_id))
        if result.scalars().first():
            raise HTTPException(status_code=400, detail=ErrorCode.SESSION_ALREADY_EXISTS)
        try:
            api_resp = await wa_api.create_session(data.session_id)
            if not api_resp:
                raise HTTPException(status_code=400, detail=ErrorCode.API_CREATE_FAILED)
        except Exception as e:
            raise HTTPException(status_code=500, detail=ErrorCode.API_CREATE_FAILED)
        status = api_resp.get("status")
        db_obj = WhatsAppSession(
            session_id=data.session_id,
            user_id=user_id,
            phone_number=data.phone_number,
            status=status,
            is_active=True,
        )
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return WhatsAppSessionOut.model_validate(db_obj)
    except SQLAlchemyError as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=ErrorCode.DB_ERROR)

async def get_qr_for_session(db: AsyncSession, session_id: str) -> Optional[str]:
    wa_api = get_wa_api()
    try:
        resp = await wa_api.get_qr(session_id)
        if not resp:
            raise HTTPException(status_code=400, detail=ErrorCode.API_QR_FAILED)
        qr_code = resp.get("qr")
        if qr_code:
            result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == session_id))
            db_obj = result.scalars().first()
            if db_obj:
                db_obj.qr_code = qr_code
                await db.commit()
                await db.refresh(db_obj)
        return qr_code
    except Exception as e:
        raise HTTPException(status_code=500, detail=ErrorCode.API_QR_FAILED)

async def get_whatsapp_session_status(db: AsyncSession, session_id: str) -> Optional[str]:
    wa_api = get_wa_api()
    try:
        status_resp = await wa_api.session_status(session_id)
        if not status_resp:
            raise HTTPException(status_code=400, detail=ErrorCode.API_STATUS_FAILED)
        status = status_resp.get("status")
        result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == session_id))
        db_obj = result.scalars().first()
        if db_obj:
            db_obj.status = status
            await db.commit()
            await db.refresh(db_obj)
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=ErrorCode.API_STATUS_FAILED)

async def delete_whatsapp_session(db: AsyncSession, session_id: str) -> bool:
    wa_api = get_wa_api()
    try:
        resp = await wa_api.delete_session(session_id)
        if not resp:
            raise HTTPException(status_code=400, detail=ErrorCode.API_DELETE_FAILED)
        result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == session_id))
        db_obj = result.scalars().first()
        if not db_obj:
            return False
        await db.delete(db_obj)
        await db.commit()
        return True
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=ErrorCode.API_DELETE_FAILED)

async def disconnect_whatsapp_session(db: AsyncSession, session_id: str) -> Optional[WhatsAppSessionOut]:
    wa_api = get_wa_api()
    try:
        resp = await wa_api.disconnect_session(session_id)
        if not resp:
            raise HTTPException(status_code=400, detail=ErrorCode.API_DISCONNECT_FAILED)
        status = resp.get("status")
        result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == session_id))
        db_obj = result.scalars().first()
        if db_obj:
            db_obj.status = status
            db_obj.is_active = False
            await db.commit()
            await db.refresh(db_obj)
            return WhatsAppSessionOut.model_validate(db_obj)
        return None
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=ErrorCode.API_DISCONNECT_FAILED)

async def connect_whatsapp_session(db: AsyncSession, session_id: str) -> Optional[WhatsAppSessionOut]:
    wa_api = get_wa_api()
    try:
        resp = await wa_api.connect_session(session_id)
        if not resp:
            raise HTTPException(status_code=400, detail=ErrorCode.API_CONNECT_FAILED)

        status = resp.get("status")
        result = await db.execute(select(WhatsAppSession).where(WhatsAppSession.session_id == session_id))
        db_obj = result.scalars().first()
        if db_obj:
            db_obj.status = status
            db_obj.is_active = True
            await db.commit()
            await db.refresh(db_obj)
            return WhatsAppSessionOut.model_validate(db_obj)
        return None
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=ErrorCode.API_CONNECT_FAILED)

async def get_user_session_by_id(user_id: UUID, session_id: str, db: AsyncSession) -> Optional[WhatsAppSessionOut]:
    try:
        result = await db.execute(
            select(WhatsAppSession).where(
                WhatsAppSession.session_id == session_id,
                WhatsAppSession.user_id == user_id
            )
        )
        db_obj = result.scalars().first()
        if db_obj:
            return WhatsAppSessionOut.model_validate(db_obj)
        return None
    except Exception as e:
        raise HTTPException(status_code=500, detail=ErrorCode.SESSION_NOT_FOUND)

async def get_all_user_sessions(user_id: UUID, db: AsyncSession) -> List[WhatsAppSessionOut]:
    try:
        result = await db.execute(
            select(WhatsAppSession).where(
                WhatsAppSession.user_id == user_id
            )
        )
        db_objs = result.scalars().all()
        return [WhatsAppSessionOut.model_validate(obj) for obj in db_objs]
    except Exception as e:
        raise HTTPException(status_code=500, detail=ErrorCode.NO_USER_SESSIONS)

async def set_active_session(user_id: UUID, db: AsyncSession, session_id: str) -> None:
    try:
        # Fetch all session ORM objects for the user
        result = await db.execute(
            select(WhatsAppSession).where(
                WhatsAppSession.user_id == user_id
            )
        )
        db_objs = result.scalars().all()
        if not db_objs:
            raise HTTPException(status_code=404, detail=ErrorCode.NO_USER_SESSIONS)
        found = False
        for obj in db_objs:
            if obj.session_id == session_id:
                obj.is_active = True
                found = True
            else:
                obj.is_active = False
        if not found:
            raise HTTPException(status_code=404, detail=ErrorCode.SESSION_NOT_FOUND)
        await db.commit()
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=ErrorCode.SET_ACTIVE_FAILED)
