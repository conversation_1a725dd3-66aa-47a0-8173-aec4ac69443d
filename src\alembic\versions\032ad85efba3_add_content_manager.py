"""Add CONTENT_MANAGER

Revision ID: 032ad85efba3
Revises: 9ac70e113ace
Create Date: 2025-09-20 19:50:05.481929

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '032ad85efba3'
down_revision: Union[str, None] = '9ac70e113ace'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE userrole ADD VALUE IF NOT EXISTS 'CONTENT_MANAGER';")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
