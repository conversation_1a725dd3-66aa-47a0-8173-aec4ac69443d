# src/routes/admin_stats_router.py
from fastapi import APIRouter, HTTPException, Request, Depends, Query
from typing import List
from uuid import UUID
from src.routes.deps import SessionDep, CurrentUserUpgrade, get_current_user_upgrade
from src.utils.handle_router_exceptions import handle_router_exceptions
from src.utils.Error_Handling import ErrorCode
from src.services.roles import get_contain_manger_user

from src.schemas.stats import (
    CourseEngagement, LessonEngagement,
    AssessmentPerformance, AssessmentRetryStat,
    CourseCompletionStat, CourseUsers, LessonUsers, AssessmentUsers
)
from src.services.admin_stats import (
    get_courses_overview, get_lessons_engagement,
    get_assessment_performance, get_assessment_retries,
    get_courses_completion, get_course_users, get_lesson_users, get_assessment_users
)

admin_stats_router = APIRouter(
    prefix="/admin/stats",
    tags=["Admin Statistics"],
    dependencies=[Depends(get_current_user_upgrade)]
)

async def _ensure_admin(user_id: CurrentUserUpgrade, db: SessionDep):
    # Reuse your existing content-manager/admin check
    user = await get_contain_manger_user(user_id, db)
    if not user:
        raise HTTPException(status_code=403, detail=ErrorCode.ALLOW_TO_CONTENT_MANAGER_ONLY)


@admin_stats_router.get("/courses/overview", response_model=List[CourseEngagement])
@handle_router_exceptions
async def courses_overview_ep(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    await _ensure_admin(user_id, db)
    return await get_courses_overview(db)


@admin_stats_router.get("/lessons/engagement", response_model=List[LessonEngagement])
@handle_router_exceptions
async def lessons_engagement_ep(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    await _ensure_admin(user_id, db)
    return await get_lessons_engagement(db)


@admin_stats_router.get("/assessments/performance", response_model=List[AssessmentPerformance])
@handle_router_exceptions
async def assessments_performance_ep(
    request: Request,
    user_id: CurrentUserUpgrade,
    db: SessionDep,
    pass_threshold: float = Query(0.6, ge=0.0, le=1.0)
):
    await _ensure_admin(user_id, db)
    return await get_assessment_performance(db, pass_threshold=pass_threshold)


@admin_stats_router.get("/assessments/retries", response_model=List[AssessmentRetryStat])
@handle_router_exceptions
async def assessments_retries_ep(
    request: Request,
    user_id: CurrentUserUpgrade,
    db: SessionDep,
    pass_threshold: float = Query(0.6, ge=0.0, le=1.0)
):
    await _ensure_admin(user_id, db)
    return await get_assessment_retries(db, pass_threshold=pass_threshold)


@admin_stats_router.get("/courses/completion", response_model=List[CourseCompletionStat])
@handle_router_exceptions
async def courses_completion_ep(request: Request, user_id: CurrentUserUpgrade, db: SessionDep):
    await _ensure_admin(user_id, db)
    return await get_courses_completion(db)



#################################################################
#################################################################


# Return the actual users engaged with a course (for contact / outreach)
@admin_stats_router.get("/courses/{course_id}/users", response_model=CourseUsers)
@handle_router_exceptions
async def course_users_ep(request: Request, user_id: CurrentUserUpgrade, course_id: UUID, db: SessionDep):
    await _ensure_admin(user_id, db)
    return await get_course_users(db, course_id)

# Return the actual viewers/completers of a lesson
@admin_stats_router.get("/lessons/{lesson_id}/users", response_model=LessonUsers)
@handle_router_exceptions
async def lesson_users_ep(request: Request, user_id: CurrentUserUpgrade, lesson_id: UUID, db: SessionDep):
    await _ensure_admin(user_id, db)
    return await get_lesson_users(db, lesson_id)

# Return pass/fail users and high-retry users for an assessment
@admin_stats_router.get("/assessments/{assessment_id}/users", response_model=AssessmentUsers)
@handle_router_exceptions
async def assessment_users_ep(
    request: Request,
    user_id: CurrentUserUpgrade,
    assessment_id: UUID,
    db: SessionDep,
    pass_threshold: float = Query(0.6, ge=0.0, le=1.0)
):
    await _ensure_admin(user_id, db)
    return await get_assessment_users(db, assessment_id, pass_threshold)
