import asyncio
import httpx
from typing import Optional, List, Dict, Any, Union
import time
from config import settings

class WhatsAppMessenger:
    _instance = None
    _lock = asyncio.Lock()
    
    MAX_PER_DAY = 800

    def __init__(self):
        self.queue = asyncio.Queue()
        self.total_sent_today = 0
        self.last_reset = time.time()
        self.running = False

    @classmethod
    async def get_instance(cls):
        async with cls._lock:
            if cls._instance is None:
                cls._instance = WhatsAppMessenger()
            return cls._instance

    async def queue_message(self, session_id: str, phone_number: Union[str, List[str]], message: str):
        await self._reset_if_needed()
        if self.total_sent_today >= self.MAX_PER_DAY:
            print("Daily limit reached!")
            return

        def calc_human_delay(msg):
            return max(5, len(msg) // 40)  # 1s per 40 chars, min 5s

        if isinstance(phone_number, list):
            delay = max(7, calc_human_delay(message))
            # Send in batches of 3
            for i in range(0, len(phone_number), 3):
                batch = phone_number[i:i+3]
                for pn in batch:
                    await self.queue.put((session_id, pn, message, delay))
                await asyncio.sleep(delay)
        else:
            delay = calc_human_delay(message)
            await self.queue.put((session_id, phone_number, message, delay))

    async def _send_to_server(self, session_id: str, formatted_number: str, message: str):
        print(f"[{time.strftime('%X')}] Sending to {formatted_number} (session {session_id}): {message}")

    async def worker(self):
        self.running = True
        while True:
            batch = []
            delay = 7  # Default min delay

            while len(batch) < 3:
                try:
                    item = await asyncio.wait_for(self.queue.get(), timeout=2)
                    batch.append(item)
                except asyncio.TimeoutError:
                    break

            if not batch:
                await asyncio.sleep(1)
                continue

            if self.total_sent_today + len(batch) > self.MAX_PER_DAY:
                print("Daily limit reached! Stopping further sends.")
                self.running = False
                break

            # Now each item is (session_id, formatted_number, message, msg_delay)
            for session_id, formatted_number, message, msg_delay in batch:
                await self._send_to_server(session_id, formatted_number, message)
                self.total_sent_today += 1
                delay = max(delay, msg_delay)  # Use the largest delay in batch

            print(f"Batch of {len(batch)} sent, waiting {delay} seconds (plus human pause 5s)")
            await asyncio.sleep(delay)
            await asyncio.sleep(5)  # Human-like pause

    async def run(self):
        if not self.running:
            await self.worker()

    async def _reset_if_needed(self):
        now = time.time()
        if now - self.last_reset > 86400:  # 24 hours
            self.total_sent_today = 0
            self.last_reset = now




class WhatsAppApiClient:
    def __init__(self):
        self.api_key = settings.WHTASAPP_API_KEY.s.rstrip("/")
        self.base_url = settings.WHTASAPP_URL
        self.headers = {
            "NA-WHA-API-KEY": self.api_key
        }

    # -------- Session Management --------
    async def create_session(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/create-session"
        payload = {"sessionId": session_id}
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload)
            resp.raise_for_status()
            return resp.json()

    async def list_sessions(self) -> Dict[str, Any]:
        url = f"{self.base_url}/sessions"
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=self.headers)
            resp.raise_for_status()
            return resp.json()

    async def get_qr(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/get-qr/{session_id}"
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=self.headers)
            if resp.status_code == 404:
                return {"error": "QR not found or not required now."}
            resp.raise_for_status()
            return resp.json()

    async def session_status(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/session-status/{session_id}"
        async with httpx.AsyncClient() as client:
            resp = await client.get(url, headers=self.headers)
            resp.raise_for_status()
            return resp.json()

    async def disconnect_session(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/disconnect-session"
        payload = {"sessionId": session_id}
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload)
            resp.raise_for_status()
            return resp.json()

    async def connect_session(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/connect-session"
        payload = {"sessionId": session_id}
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload)
            resp.raise_for_status()
            return resp.json()

    async def delete_session(self, session_id: str) -> Dict[str, Any]:
        url = f"{self.base_url}/delete-session"
        payload = {"sessionId": session_id}
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload)
            resp.raise_for_status()
            return resp.json()

    # -------- Messaging --------
    async def send_text_message(self, session_id: str, recipient_number: str, message: str) -> Dict[str, Any]:
        url = f"{self.base_url}/send-message"
        payload = {
            "sessionId": session_id,
            "recipient_number": recipient_number,
            "message": message
        }
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers={**self.headers, "Content-Type": "application/json"}, json=payload)
            resp.raise_for_status()
            return resp.json()

    async def send_media_message(self, session_id: str, recipient_number: str, file_path: str, message: Optional[str] = None) -> Dict[str, Any]:
        url = f"{self.base_url}/send-message"
        data = {
            "sessionId": session_id,
            "recipient_number": recipient_number
        }
        if message:
            data["message"] = message

        files = {"media": open(file_path, "rb")}
        async with httpx.AsyncClient() as client:
            resp = await client.post(url, headers=self.headers, data=data, files=files)
            resp.raise_for_status()
            return resp.json()

# -------- Example Usage --------
# import asyncio
# async def main():
#     client = WhatsAppApiClient(api_key="your_api_key", base_url="http://localhost:5001")
#     # 1. Create a session
#     resp = await client.create_session("instance_1")
#     print(resp)
#     # 2. Send a message
#     msg_resp = await client.send_text_message("instance_1", "+201234567890", "Hello from Python!")
#     print(msg_resp)
#     # 3. Get session status
#     print(await client.session_status("instance_1"))
#
# asyncio.run(main())
