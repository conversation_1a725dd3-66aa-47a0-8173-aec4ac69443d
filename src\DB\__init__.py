# app/DB/__init__.py
from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession

# __all__ = [
#     "Base", "get_sessionmaker", "get_db",
#     "User", "UserAuth", "Country", "RefreshToken", "ContactUsSubmission",
#     "Assessment", "Dimension", "Question", "Option", "ResultProfile",
#     "UserSession", "Response", "SessionProfile",
# ]

from src.utils.logger import AdvancedLogger
from src.DB.database import Base, get_sessionmaker
from src.DB.models.users import User, UserAuth, Country, RefreshToken, ContactUsSubmission
from src.DB.models.assessments import (Assessment, Dimension, Question,Option, 
                                       Course, CourseContent, Lesson,
                                       ResultProfile, UserSession, Response, SessionProfile,OptionScore, UserLessonProgress)


logger = AdvancedLogger(name=__name__)


# Dependency function to provide a database session for FastAPI
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Database session dependency that properly yields the session"""
    session_maker = get_sessionmaker()
    session = session_maker()
    try:
        yield session
    except Exception as e:
        logger.error(f"Database error: {e}")
        try:
            await session.rollback()
        except Exception as rollback_error:
            logger.error(f"Error during rollback: {rollback_error}")
        raise e
    finally:
        # Safely close the session with error handling
        try:
            await session.close()
            logger.debug("Session closed")
        except Exception as close_error:
            # Just log the error but don't raise - this prevents event loop errors
            logger.warning(f"Error closing session: {close_error}")
