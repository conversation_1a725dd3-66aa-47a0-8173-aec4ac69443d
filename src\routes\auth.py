import src.schemas.user
import src.services.auth
from src.utils.Error_Handling import ErrorCode
from src.utils.logger import AdvancedLogger
from fastapi import APIRouter, HTTPException, Depends, status, Response, Request
from fastapi.security import OAuth2PasswordRequestForm
from src.schemas.user import SocialLoginRequest
from src.schemas import auth as user_schema
from src.services import auth as auth_services
from src.routes.deps import SessionDep, CurrentUserUpgrade
from src.utils.user_utils import get_refresh_token, revoke_refresh_token
from src.utils.Error_Handling import ErrorCode
from src.services.auth import verify_recaptcha
from config import settings
from src.utils.handle_router_exceptions import handle_router_exceptions, ErrorResponse

logger = AdvancedLogger(name=__name__)

auth_router = APIRouter(
    tags=["Authentication"],
)

auth_router = APIRouter(
    tags=["Authentication"],
)

@auth_router.post(
    "/login",
    response_model=user_schema.TokenData,
    responses={
        200: {"model": user_schema.TokenData, "description": "Authentication successful"},
        400: {"model": ErrorResponse, "description": "Invalid credentials or account locked"},
        401: {"model": ErrorResponse, "description": "Unauthorized - Invalid credentials"},
        403: {"model": ErrorResponse, "description": "In GENERAL --- Forbidden - Insufficient permissions"},
        403: {"model": ErrorResponse, "description": "Account not verified or locked"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def login_user_service(request: Request,
                           response: Response,
                           db: SessionDep,
                           form_data: OAuth2PasswordRequestForm = Depends()):
    """Authenticates the user and returns access/refresh tokens"""
    auth_response = await auth_services.login_user(request, response, form_data, db)
    return user_schema.TokenData.model_validate(auth_response)

# @auth_router.post(
#     "/social-login",
#     response_model=user_schema.TokenData,
#     responses={
#         200: {"model": user_schema.TokenData, "description": "Social authentication successful"},
#         400: {"model": ErrorResponse, "description": "Invalid social token or missing user info"},
#         401: {"model": ErrorResponse, "description": "Unauthorized - Invalid social token"},
#         422: {"model": ErrorResponse, "description": "Validation error"},
#         500: {"model": ErrorResponse, "description": "Internal server error"},
#     },
#     status_code=status.HTTP_200_OK
# )
# @handle_router_exceptions
# async def social_login_endpoint(
#     request: Request,
#     payload: SocialLoginRequest,
#     response: Response,
#     db: SessionDep
# ) -> user_schema.TokenData:
#     """Authenticates user using social provider (Google/Facebook) credentials"""
#     auth_response = await auth_services.social_login(request=request, payload=payload, response=response, db=db)
#     return user_schema.TokenData.model_validate(auth_response)

@auth_router.post(
    "/account",
    response_model=src.schemas.user.UserRead,
    responses={
        201: {"model": src.schemas.user.UserRead, "description": "User registered successfully"},
        400: {"model": ErrorResponse, "description": "Email already exists or invalid data"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_201_CREATED
)
@handle_router_exceptions
async def register_user(request: Request,
                      db: SessionDep,
                      user_data: src.schemas.user.UserCreate):
    """Registers a new user and sends a verification code to their email"""
    existing_email = await auth_services.get_user_by_email_or_username(db, user_data.email)
    if existing_email:
        raise HTTPException(status_code=400, detail=ErrorCode.EXIST_EMAIL)
    if user_data.country_id == 0:
        user_data.country_id = None
    user = await src.services.auth.create_user(user_data, db)
    return src.schemas.user.UserRead.model_validate(user)

@auth_router.post(
    "/account-admin",
    response_model=src.schemas.user.UserRead,
    responses={
        201: {"model": src.schemas.user.UserRead, "description": "User registered successfully"},
        400: {"model": ErrorResponse, "description": "Email already exists or invalid data"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_201_CREATED
)
@handle_router_exceptions
async def register_user(request: Request,
                    user_id: CurrentUserUpgrade,
                      db: SessionDep,
                      user_data: src.schemas.user.UserCreateAdmin):
    """Registers a new user and sends a verification code to their email"""
    admin = await auth_services.get_user_roles(db, user_id)
    if not admin:
        raise HTTPException(status_code=403, detail=ErrorCode.NOT_ADMIN)
    existing_email = await auth_services.get_user_by_email_or_username(db, user_data.email)
    if existing_email:
        raise HTTPException(status_code=400, detail=ErrorCode.EXIST_EMAIL)
    if user_data.country_id == 0:
        user_data.country_id = None
    user = await src.services.auth.create_user_by_admin(user_data, db)
    return src.schemas.user.UserRead.model_validate(user)


@auth_router.post(
    "/verify-registration",
    responses={
        202: {"description": "Registration verified successfully"},
        400: {"model": ErrorResponse, "description": "Invalid verification code"},
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_202_ACCEPTED
)
@handle_router_exceptions
async def confirming_registration(request: Request, confirmation_request: user_schema.RegistrationConfirmation, db: SessionDep):
    """Confirms user registration using the verification code"""
    user = await auth_services.get_user_by_email_or_username(db, confirmation_request.email)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    await auth_services.confirm_email(user, confirmation_request, db)

@auth_router.post(
    "/send-code",
    responses={
        200: {"description": "Verification code sent successfully"},
        400: {"model": ErrorResponse, "description": "Invalid reCAPTCHA token"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Failed to send verification code"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def send_verification_code_to_email(request: Request, user_data: user_schema.EmailData, db: SessionDep):
    """Send verification code to the user's email, used for password reset"""
    await verify_recaptcha(user_data.recaptcha_token)
    user = await auth_services.get_user_by_email_or_username(db, user_data.email)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    email_sent = await auth_services.send_email_with_vc_for_rest_password(user, db, request)
    if not email_sent:
        raise HTTPException(status_code=500, detail=ErrorCode.FAILED_TO_SEND_EMAIL)

@auth_router.put(
    "/reset-password",
    responses={
        202: {"description": "Password reset successfully"},
        400: {"model": ErrorResponse, "description": "Invalid verification code or passwords don't match"},
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        404: {"model": ErrorResponse, "description": "User not found"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_202_ACCEPTED
)
@handle_router_exceptions
async def update_password(request: Request, user_data: user_schema.PasswordResetRequest, db: SessionDep):
    """Update user's password after verification"""
    user = await auth_services.get_user_by_email_or_username(db, user_data.email)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    await auth_services.user_verification_code(user, user_data, db)

@auth_router.post(
    "/refresh",
    response_model=user_schema.TokenData,
    responses={
        200: {"model": user_schema.TokenData, "description": "New access token generated successfully"},
        401: {"model": ErrorResponse, "description": "Invalid or expired refresh token"},
        403: {"model": ErrorResponse, "description": "Refresh token revoked or invalid"},
        422: {"model": ErrorResponse, "description": "Validation error"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    },
    status_code=status.HTTP_200_OK
)
@handle_router_exceptions
async def refresh_token(request: Request, response: Response, db: SessionDep) -> user_schema.TokenData:
    """Generates a new access token using a valid refresh token"""
    new_access_token = await get_refresh_token(request, response, db)
    return user_schema.TokenData(
        access_token=new_access_token
    )

@auth_router.post(
    "/revoke",
    responses={
        200: {"description": "Token revoked successfully"},
        401: {"model": ErrorResponse, "description": "Unauthorized"},
        404: {"model": ErrorResponse, "description": "User not found"},
        500: {"model": ErrorResponse, "description": "Internal server error"},
    }
)
@handle_router_exceptions
async def revoke_token(request: Request, response: Response, db: SessionDep, user_id: CurrentUserUpgrade):
    """Revokes the current refresh token (logs user out)"""
    user = await src.services.auth.get_user_by_id(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail=ErrorCode.USER_NOT_FOUND)
    delete_rt = await revoke_refresh_token(
        user=user,
        request=request,
        response=response,
        db=db,
    )
    if delete_rt:
        response.delete_cookie(
            key="refresh_token",
            path="/",
            samesite="lax",
            secure=True if settings.ENVIRONMENT != 'local' else False,
        )

    response.status_code = status.HTTP_200_OK
    return response