alembic==1.13.2
annotated-types==0.7.0
anyio==4.9.0
APScheduler==3.11.0
async-timeout==5.0.1
asyncpg==0.29.0
bcrypt==3.2.2
cachetools==6.1.0
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.1.8
colorama==0.4.6
cryptography==45.0.5
cssselect==1.3.0
cssutils==2.11.1
Deprecated==1.2.18
dnspython==2.7.0
ecdsa==0.19.1
email_validator==2.2.0
fastapi==0.115.11
fastapi-events==0.12.0
greenlet==3.2.3
h11==0.16.0
httpcore==1.0.9
httpx==0.28.1
idna==3.10
Jinja2==3.1.6
limits==5.4.0
# lxml==6.0.0
Mako==1.3.10
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
more-itertools==10.7.0
# numpy==2.3.2
packaging==25.0
# pandas==2.2.3
passlib==1.7.4
premailer==3.10.0
# psycopg2==2.9.10
psycopg2-binary==2.9.10
pyasn1==0.6.1
pycparser==2.22
pydantic==2.10.6
pydantic-settings==2.8.1
pydantic_core==2.27.2
Pygments==2.19.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-jose==3.5.0
python-multipart==0.0.20
pytz==2025.2
requests==2.32.4
rich==14.1.0
rsa==4.9.1
six==1.17.0
slowapi==0.1.9
sniffio==1.3.1
SQLAlchemy==2.0.42
starlette==0.46.1
typing_extensions==4.12.2
tzdata==2025.2
tzlocal==5.3.1
ua-parser==1.0.1
ua-parser-builtins==0.18.0.post1
urllib3==2.5.0
user-agents==2.2.0
uvicorn==0.34.0
wrapt==1.17.2
