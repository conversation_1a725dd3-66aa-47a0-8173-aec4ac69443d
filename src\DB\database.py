import ssl
import logging
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import NullPool
from config import settings

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)

Base = declarative_base()

engine = None
nullpool_engine = None
async_session_maker = None


def prepare_database_url(url: str) -> tuple[str, bool]:
    url = url.replace('\\x3a', ':')

    if url.startswith("postgres://"):
        url = url.replace("postgres://", "postgresql+asyncpg://", 1)
    elif url.startswith("postgresql://"):
        url = url.replace("postgresql://", "postgresql+asyncpg://", 1)

    parsed = urlparse(url)
    query_params = parse_qs(parsed.query)

    sslmode = query_params.get('sslmode', [''])[0]
    use_ssl = sslmode == 'require'

    query_params.pop('sslmode', None)
    query_params.pop('channel_binding', None)  # We don't support this in connect_args

    new_query = urlencode(query_params, doseq=True)
    cleaned_url = urlunparse(
        (parsed.scheme, parsed.netloc, parsed.path,
         parsed.params, new_query, parsed.fragment)
    )

    return cleaned_url, use_ssl

db_url, _ = prepare_database_url(settings.DATABASE_URL)
SQLALCHEMY_DATABASE_URL = db_url


def get_engine():
    """Create or return the pooled async engine"""
    global engine
    if engine is None:
        # logger.info("Creating new pooled async engine")

        db_url, use_ssl = prepare_database_url(settings.DATABASE_URL)

        connect_args = {
            "server_settings": {
                "application_name": "Mr.John - Course server",
                "statement_timeout": f"{settings.POOL_TIMEOUT}",
                "idle_in_transaction_session_timeout": f"{settings.POOL_TIMEOUT}",
            }
        }

        if use_ssl:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_REQUIRED
            connect_args["ssl"] = ssl_context
            # logger.info("SSL is enabled (sslmode=require)")

        engine = create_async_engine(
            db_url,
            pool_size=settings.POOL_SIZE,
            max_overflow=settings.MAX_OVERFLOW,
            pool_timeout=settings.POOL_TIMEOUT,
            pool_recycle=settings.POOL_RECYCLE,
            pool_pre_ping=True,
            echo=False,
            connect_args=connect_args,
            future=True,
        )

        logger.info(f"Pooled async engine created successfully with pool size: {settings.POOL_SIZE} and max overflow: {settings.MAX_OVERFLOW}")
    return engine


def get_nullpool_engine():
    """Create or return an async engine using NullPool (no connection pooling)"""
    global nullpool_engine
    if nullpool_engine is None:
        # logger.info("Creating new async engine with NullPool")

        db_url, use_ssl = prepare_database_url(settings.DATABASE_URL)

        connect_args = {}
        if use_ssl:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_REQUIRED
            connect_args["ssl"] = ssl_context
            # logger.info("SSL is enabled (sslmode=require)")

        nullpool_engine = create_async_engine(
            db_url,
            poolclass=NullPool,
            echo=False,
            connect_args=connect_args,
            future=True,
        )

        logger.info("Async engine with NullPool created successfully")
    return nullpool_engine


def get_sessionmaker():
    global async_session_maker
    if async_session_maker is None:
        if settings.NULL_POOL is True:
            engine = get_nullpool_engine()
        else:
            engine = get_engine()
        async_session_maker = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=False,
            autocommit=False
        )
    return async_session_maker
