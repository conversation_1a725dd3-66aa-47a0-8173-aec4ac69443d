from jinja2 import Template, Environment, BaseLoader
from premailer import transform
from typing import Optional
from src.utils.Email.email_components import  generate_signature, _render_template, base_email_template

# Initialize Jinja2 environment for consistent template rendering
env = Environment(loader=BaseLoader())

def email_verification_template(first_name: str, email_verification_code: str) -> str:
    """Generate an email verification template."""
    content = f"""
    <p>Hi <strong>{first_name}</strong>,</p>
    <p>Thank you for signing up with the Mr.John Edward platform! To verify your email address, please use the code below:</p>
    <div class="highlight-box" style="text-align:center;font-size:24px;font-weight:bold;">
        {email_verification_code}
    </div>
    <p>If you didn't request this, please ignore this email or contact our support team.</p>
    """
    return _render_template(base_email_template("Email Verification", content))


def email_verification_template_for_admin_user(name: str, Password: str) -> str:
    """Generate an email verification template."""
    content = f"""
    <p>Hi <strong>{name}</strong>,</p>
    <p>Welcome to the Mr.John Edward platform! To set your password, please use the code below:</p>
    <div class="highlight-box" style="text-align:center;font-size:24px;font-weight:bold;">
        {Password}
    </div>
    <p>This is your temporary password, please change it after login.</p>
    <p>If you didn't request this, please ignore this email or contact our support team.</p>
    """
    return _render_template(base_email_template("Access Setup", content))



def password_reset_verification_template(first_name: str, reset_code: str) -> str:
    """Generate a password reset verification template."""
    content = f"""
    <p>Hi <strong>{first_name}</strong>,</p>
    <p>We received a request to reset your password. Use the code below to proceed:</p>
    <div class="highlight-box" style="text-align:center;font-size:24px;font-weight:bold;">
        {reset_code}
    </div>
    <p>This code expires in 15 minutes. For your security, please do not share this code with anyone.</p>
    <p>If you didn't request this password reset, please secure your account immediately.</p>
    """
    return _render_template(base_email_template("Password Reset Request", content))
